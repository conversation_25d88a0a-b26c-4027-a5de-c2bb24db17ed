<div class="md:p-6 space-y-6">
    <!-- <PERSON><PERSON><PERSON> Header Section -->
    <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 rounded-xl p-6 border border-blue-100 dark:border-slate-600">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex flex-col md:flex-row md:items-center gap-4">
                <div class="flex-shrink-0">
                    <div class="h-12 w-12 bg-blue-500 rounded-xl flex items-center justify-center">
                        <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-slate-900 dark:text-white">Cases Management</h1>
                    <p class="mt-1 text-sm text-slate-600 dark:text-slate-400">
                        Track and manage dental cases from submission to completion
                    </p>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="flex flex-col md:flex-row md:items-center gap-4">
                <div
                    class="bg-white dark:bg-slate-800 flex-1 md:flex-auto rounded-lg px-4 py-2 border border-slate-200 dark:border-slate-600">
                    <div class="text-xs text-slate-500 dark:text-slate-400">Total Cases</div>
                    <div class="text-lg font-semibold text-slate-900 dark:text-white">{{ $totalCases }}</div>
                </div>
                <div
                    class="bg-white dark:bg-slate-800 flex-1 md:flex-auto rounded-lg px-4 py-2 border border-slate-200 dark:border-slate-600">
                    <div class="text-xs text-slate-500 dark:text-slate-400">Active</div>
                    <div class="text-lg font-semibold text-yellow-600">{{ $activeCases }}</div>
                </div>
                <div
                    class="bg-white dark:bg-slate-800 flex-1 md:flex-auto rounded-lg px-4 py-2 border border-slate-200 dark:border-slate-600">
                    <div class="text-xs text-slate-500 dark:text-slate-400">Completed</div>
                    <div class="text-lg font-semibold text-green-600">{{ $completedCases }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters Section -->
    <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <!-- Search and Filters -->
            <div class="flex flex-col sm:flex-row gap-3 flex-1">
                <!-- Search Input -->
                <div class="flex-1 min-w-[200px]">
                    <flux:input wire:model.live.debounce.300ms="search"
                        placeholder="Search cases, patients, or clients..." icon="magnifying-glass" class="w-full" />
                </div>

                <!-- Clear Filters Button -->
                <div class="flex items-center">
                    @if ($search || $statusFilter !== '' || $priorityFilter !== '' || $clientFilter !== '')
                        <button wire:click="clearFilters"
                            class="inline-flex items-center px-3 py-2 text-sm text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors duration-200"
                            title="Clear all filters">
                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    @endif
                </div>

                <!-- Status Filter -->
                <div class="min-w-[140px]">
                    <flux:select wire:model.live="statusFilter" placeholder="All Status">
                        <flux:select.option value="">All Status</flux:select.option>
                        <flux:select.option value="Received">🔵 Received</flux:select.option>
                        <flux:select.option value="Working">🟡 Working</flux:select.option>
                        <flux:select.option value="Pending">🟠 Pending</flux:select.option>
                        <flux:select.option value="Shipped">✅ Shipped</flux:select.option>
                    </flux:select>
                </div>

                <!-- Priority Filter -->
                <div class="min-w-[120px]">
                    <flux:select wire:model.live="priorityFilter" placeholder="All Priority">
                        <flux:select.option value="">All Priority</flux:select.option>
                        <flux:select.option value="Normal">⚪ Normal</flux:select.option>
                        <flux:select.option value="Urgent">🔴 Urgent</flux:select.option>
                    </flux:select>
                </div>

                <!-- Client Filter -->
                <div class="min-w-[160px]">
                    <flux:select wire:model.live="clientFilter" placeholder="All Clients">
                        <flux:select.option value="">All Clients</flux:select.option>
                        @foreach ($dentists as $dentist)
                            <flux:select.option value="{{ $dentist->id }}">{{ $dentist->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
            </div>

            <!-- Enhanced Add Case Button -->
            <div class="flex items-center gap-3">
                <flux:tooltip content="Add a new case to the system">
                    <flux:button wire:click="openCreateModal" variant="primary" icon="plus" class="shadow-sm">
                        Add Case
                    </flux:button>
                </flux:tooltip>
            </div>
        </div>
    </div>

    <!-- Enhanced Cases Table -->
    <div
        class="bg-white dark:bg-slate-800 shadow-sm rounded-lg overflow-hidden border border-slate-200 dark:border-slate-700">
        <!-- Table Header with Count -->
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <h3 class="text-lg font-medium text-slate-900 dark:text-white">Cases Directory</h3>
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {{ $cases->total() }} {{ Str::plural('case', $cases->total()) }}
                    </span>
                </div>
                @if ($search || $statusFilter !== '' || $priorityFilter !== '' || $clientFilter !== '')
                    <button wire:click="clearFilters"
                        class="text-sm text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors duration-200">
                        Clear filters
                    </button>
                @endif
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                <thead class="bg-slate-50 dark:bg-slate-700">
                    <tr>
                        <th
                            class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            <div class="flex items-center space-x-1">
                                <span>Case Details</span>
                                <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                    </path>
                                </svg>
                            </div>
                        </th>
                        <th
                            class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            <div class="flex items-center space-x-1">
                                <span>Client</span>
                                <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </th>
                        <th
                            class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            Status</th>
                        <th
                            class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            Priority</th>
                        <th
                            class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            Files</th>
                        <th
                            class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            Submitted</th>
                        <th
                            class="px-6 py-4 text-right text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                    @forelse ($cases as $case)
                        <tr class="hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors duration-200">
                            <td class="px-6 py-4">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <div
                                            class="h-10 w-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-medium text-white">
                                                {{ strtoupper(substr($case->case_title, 0, 2)) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                            {{ $case->case_title }}
                                        </div>
                                        @if ($case->patient_name)
                                            <div class="text-xs text-slate-500 dark:text-slate-400 truncate">
                                                Patient: {{ $case->patient_name }}
                                            </div>
                                        @endif
                                        <div class="text-xs text-slate-500 dark:text-slate-400 truncate mt-1">
                                            {{ Str::limit($case->case_description, 60) }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center space-x-2">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div
                                            class="h-8 w-8 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-full flex items-center justify-center">
                                            <span class="text-xs font-medium text-white">
                                                {{ $case->client->initials() }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-slate-900 dark:text-white">
                                            {{ $case->client->name }}
                                        </div>
                                        <div class="text-xs text-slate-500 dark:text-slate-400">
                                            {{ $case->client->email }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center space-x-2">
                                    <flux:badge variant="{{ $case->status_badge_color }}" size="sm">
                                        <flux:dropdown>
                                            <flux:button icon:trailing="chevron-down" variant="ghost" size="xs"
                                                class="text-slate-400 hover:text-slate-600">
                                                {{ $case->status }}
                                            </flux:button>

                                            <flux:menu>
                                                @foreach (['Received', 'Working', 'Pending', 'Shipped'] as $status)
                                                    @if ($status !== $case->status)
                                                        <flux:menu.item
                                                            wire:click="updateStatus({{ $case->id }}, '{{ $status }}')">
                                                            {{ $status }}
                                                        </flux:menu.item>
                                                    @endif
                                                @endforeach
                                            </flux:menu>
                                        </flux:dropdown>
                                    </flux:badge>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <flux:badge variant="{{ $case->priority_badge_color }}" size="sm">
                                    {{ $case->priority }}
                                </flux:badge>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center space-x-1">
                                    <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 00-2.828-2.828z">
                                        </path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4"></path>
                                    </svg>
                                    <span class="text-sm text-slate-600 dark:text-slate-300">
                                        {{ $case->files->count() }} {{ Str::plural('file', $case->files->count()) }}
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-slate-900 dark:text-white font-medium">
                                    {{ $case->submitted_at->format('M d, Y') }}
                                </div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    {{ $case->submitted_at->format('g:i A') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-1">
                                    <!-- View Details -->
                                    <flux:tooltip content="View case details">
                                        <flux:button wire:click="openDetailsModal({{ $case->id }})"
                                            variant="ghost" size="sm" icon="eye"
                                            class="text-slate-500 hover:text-blue-600 dark:text-slate-400 dark:hover:text-blue-400 transition-colors duration-200">
                                        </flux:button>
                                    </flux:tooltip>

                                    <!-- Edit Case -->
                                    <flux:tooltip content="Edit case">
                                        <flux:button wire:click="openEditModal({{ $case->id }})" variant="ghost"
                                            size="sm" icon="pencil"
                                            class="text-slate-500 hover:text-yellow-600 dark:text-slate-400 dark:hover:text-yellow-400 transition-colors duration-200">
                                        </flux:button>
                                    </flux:tooltip>

                                    <!-- Delete Case -->
                                    <flux:tooltip content="Delete case">
                                        <flux:button wire:click="openConfirmModal({{ $case->id }})"
                                            variant="ghost" size="sm" icon="trash"
                                            class="text-slate-500 hover:text-red-600 dark:text-slate-400 dark:hover:text-red-400 transition-colors duration-200">
                                        </flux:button>
                                    </flux:tooltip>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center space-y-3">
                                    <div
                                        class="h-12 w-12 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                        <svg class="h-6 w-6 text-slate-400" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                            </path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-slate-900 dark:text-white">No cases found
                                        </h3>
                                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                                            @if ($search || $statusFilter !== '' || $priorityFilter !== '' || $clientFilter !== '')
                                                No cases match your current filters. Try adjusting your search criteria.
                                            @else
                                                Get started by adding your first case to the system.
                                            @endif
                                        </p>
                                    </div>
                                    @if (!$search && $statusFilter === '' && $priorityFilter === '' && $clientFilter === '')
                                        <flux:button wire:click="openCreateModal" variant="primary" icon="plus"
                                            class="mt-4">
                                            Add Your First Case
                                        </flux:button>
                                    @else
                                        <div class="flex items-center space-x-3 mt-4">
                                            <flux:button wire:click="clearFilters" variant="ghost" icon="x-mark">
                                                Clear Filters
                                            </flux:button>
                                            <flux:button wire:click="openCreateModal" variant="primary"
                                                icon="plus">
                                                Add Case
                                            </flux:button>
                                        </div>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Enhanced Pagination -->
        @if ($cases->hasPages())
            <div class="px-6 py-4 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/50">
                {{ $cases->links() }}
            </div>
        @endif
    </div>

    <!-- Enhanced Form Modal (Add/Edit Case) -->
    <flux:modal wire:model="showFormModal" class="max-w-3xl w-full">
        <form wire:submit="save">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="{{ $caseId ? 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z' : 'M12 6v6m0 0v6m0-6h6m-6 0H6' }}">
                            </path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-slate-900 dark:text-white">
                            {{ $caseId ? 'Edit Case' : 'Add New Case' }}
                        </h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">
                            {{ $caseId ? 'Update case information and status' : 'Create a new dental case for tracking' }}
                        </p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Client Selection -->
                    <div class="md:col-span-2">
                        <flux:field>
                            <flux:label>Client (Dentist) *</flux:label>
                            <flux:select wire:model="client_id" placeholder="Select a client">
                                @foreach ($dentists as $dentist)
                                    <flux:select.option value="{{ $dentist->id }}">{{ $dentist->name }}
                                    </flux:select.option>
                                @endforeach
                            </flux:select>
                            <flux:error name="client_id" />
                        </flux:field>
                    </div>

                    <!-- Patient Name -->
                    <div>
                        <flux:field>
                            <flux:label>Patient Name</flux:label>
                            <flux:input wire:model="patient_name" placeholder="Enter patient name (optional)" />
                            <flux:error name="patient_name" />
                        </flux:field>
                    </div>

                    <!-- Case Title -->
                    <div>
                        <flux:field>
                            <flux:label>Case Title *</flux:label>
                            <flux:input wire:model="case_title" placeholder="Enter case title" />
                            <flux:error name="case_title" />
                        </flux:field>
                    </div>

                    <!-- Status -->
                    <div>
                        <flux:field>
                            <flux:label>Status *</flux:label>
                            <flux:select wire:model="status">
                                <flux:select.option value="Received">Received</flux:select.option>
                                <flux:select.option value="Working">Working</flux:select.option>
                                <flux:select.option value="Pending">Pending</flux:select.option>
                                <flux:select.option value="Shipped">Shipped</flux:select.option>
                            </flux:select>
                            <flux:error name="status" />
                        </flux:field>
                    </div>

                    <!-- Priority -->
                    <div>
                        <flux:field>
                            <flux:label>Priority *</flux:label>
                            <flux:select wire:model="priority">
                                <flux:select.option value="Normal">Normal</flux:select.option>
                                <flux:select.option value="Urgent">Urgent</flux:select.option>
                            </flux:select>
                            <flux:error name="priority" />
                        </flux:field>
                    </div>

                    <!-- Case Description -->
                    <div class="md:col-span-2">
                        <flux:field>
                            <flux:label>Case Description *</flux:label>
                            <flux:textarea wire:model="case_description"
                                placeholder="Enter detailed case description..." rows="4" />
                            <flux:error name="case_description" />
                        </flux:field>
                    </div>
                </div>
            </div>

            <div
                class="flex items-center justify-end space-x-3 px-6 py-4 bg-slate-50 dark:bg-slate-700/50 border-t border-slate-200 dark:border-slate-600">
                <flux:button wire:click="closeFormModal" variant="ghost">
                    Cancel
                </flux:button>
                <flux:button type="submit" variant="primary" class="min-w-[100px]">
                    {{ $caseId ? 'Update Case' : 'Create Case' }}
                </flux:button>
            </div>
        </form>
    </flux:modal>

    <!-- Enhanced Confirm Delete Modal -->
    <flux:modal wire:model="showConfirmModal" class="max-w-md w-full">
        @if ($caseToDelete)
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="h-10 w-10 bg-red-500 rounded-lg flex items-center justify-center">
                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                            </path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Delete Case</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">This action cannot be undone</p>
                    </div>
                </div>

                <div class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-4 mb-6">
                    <div class="text-sm">
                        <div class="font-medium text-slate-900 dark:text-white mb-1">
                            {{ $caseToDelete->case_title }}
                        </div>
                        @if ($caseToDelete->patient_name)
                            <div class="text-slate-600 dark:text-slate-400 mb-1">
                                Patient: {{ $caseToDelete->patient_name }}
                            </div>
                        @endif
                        <div class="text-slate-500 dark:text-slate-400">
                            Client: {{ $caseToDelete->client->name }}
                        </div>
                    </div>
                </div>

                <p class="text-sm text-slate-600 dark:text-slate-400 mb-6">
                    Are you sure you want to delete this case? This will also delete all associated files and cannot be
                    undone.
                </p>
            </div>

            <div
                class="flex items-center justify-end space-x-3 px-6 py-4 bg-slate-50 dark:bg-slate-700/50 border-t border-slate-200 dark:border-slate-600">
                <flux:button wire:click="closeConfirmModal" variant="ghost">
                    Cancel
                </flux:button>
                <flux:button wire:click="delete" variant="danger">
                    Delete Case
                </flux:button>
            </div>
        @endif
    </flux:modal>

    <!-- Enhanced Details Modal -->
    <flux:modal wire:model="showDetailsModal" class="max-w-4xl w-full">
        @if ($caseDetails)
            <div class="p-6">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                </path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-slate-900 dark:text-white">Case Details</h3>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Complete case information and files
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <flux:badge variant="{{ $caseDetails->status_badge_color }}">
                            {{ $caseDetails->status }}
                        </flux:badge>
                        <flux:badge variant="{{ $caseDetails->priority_badge_color }}">
                            {{ $caseDetails->priority }}
                        </flux:badge>
                    </div>
                </div>

                <!-- Case Information Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-slate-700 dark:text-slate-300">Case Title</label>
                            <p class="mt-1 text-sm text-slate-900 dark:text-white">{{ $caseDetails->case_title }}</p>
                        </div>

                        @if ($caseDetails->patient_name)
                            <div>
                                <label class="text-sm font-medium text-slate-700 dark:text-slate-300">Patient
                                    Name</label>
                                <p class="mt-1 text-sm text-slate-900 dark:text-white">
                                    {{ $caseDetails->patient_name }}</p>
                            </div>
                        @endif

                        <div>
                            <label class="text-sm font-medium text-slate-700 dark:text-slate-300">Client</label>
                            <div class="mt-1 flex items-center space-x-2">
                                <div
                                    class="h-6 w-6 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-full flex items-center justify-center">
                                    <span class="text-xs font-medium text-white">
                                        {{ $caseDetails->client->initials() }}
                                    </span>
                                </div>
                                <div>
                                    <p class="text-sm text-slate-900 dark:text-white">{{ $caseDetails->client->name }}
                                    </p>
                                    <p class="text-xs text-slate-500 dark:text-slate-400">
                                        {{ $caseDetails->client->email }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-slate-700 dark:text-slate-300">Submitted</label>
                            <p class="mt-1 text-sm text-slate-900 dark:text-white">
                                {{ $caseDetails->submitted_at->format('M d, Y \a\t g:i A') }}
                            </p>
                        </div>

                        @if ($caseDetails->completed_at)
                            <div>
                                <label class="text-sm font-medium text-slate-700 dark:text-slate-300">Completed</label>
                                <p class="mt-1 text-sm text-slate-900 dark:text-white">
                                    {{ $caseDetails->completed_at->format('M d, Y \a\t g:i A') }}
                                </p>
                            </div>
                        @endif

                        <div>
                            <label class="text-sm font-medium text-slate-700 dark:text-slate-300">Duration</label>
                            <p class="mt-1 text-sm text-slate-900 dark:text-white">
                                {{ $caseDetails->duration_in_days }}
                                {{ Str::plural('day', $caseDetails->duration_in_days) }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Case Description -->
                <div class="mb-6">
                    <label class="text-sm font-medium text-slate-700 dark:text-slate-300">Description</label>
                    <div class="mt-1 p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <p class="text-sm text-slate-900 dark:text-white whitespace-pre-wrap">
                            {{ $caseDetails->case_description }}</p>
                    </div>
                </div>

                <!-- Files Section -->
                @if ($caseDetails->files->count() > 0)
                    <div>
                        <label class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3 block">
                            Attached Files ({{ $caseDetails->files->count() }})
                        </label>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            @foreach ($caseDetails->files as $file)
                                <div
                                    class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-3 border border-slate-200 dark:border-slate-600">
                                    <div class="flex items-start space-x-3">
                                        <div class="flex-shrink-0">
                                            <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                                </path>
                                            </svg>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            @if ($file->label)
                                                <p class="text-xs font-medium text-slate-900 dark:text-white truncate">
                                                    {{ $file->label }}</p>
                                            @endif
                                            <p class="text-xs text-slate-500 dark:text-slate-400 truncate">
                                                {{ basename($file->file_path) }}</p>
                                            <p class="text-xs text-slate-400 dark:text-slate-500">
                                                {{ $file->file_type }}</p>
                                            <p class="text-xs text-slate-400 dark:text-slate-500">
                                                Uploaded by {{ $file->uploadedBy->name }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @else
                    <div class="text-center py-6">
                        <svg class="h-12 w-12 text-slate-300 dark:text-slate-600 mx-auto mb-3" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        <p class="text-sm text-slate-500 dark:text-slate-400">No files attached to this case</p>
                    </div>
                @endif
            </div>

            <div
                class="flex items-center justify-end space-x-3 px-6 py-4 bg-slate-50 dark:bg-slate-700/50 border-t border-slate-200 dark:border-slate-600">
                <flux:button wire:click="closeDetailsModal" variant="ghost">
                    Close
                </flux:button>
                <flux:button wire:click="openEditModal({{ $caseDetails->id }})" variant="primary">
                    Edit Case
                </flux:button>
            </div>
        @endif
    </flux:modal>
</div>
