<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DentalCase extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'cases';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'patient_name',
        'case_title',
        'case_description',
        'status',
        'priority',
        'submitted_at',
        'completed_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'submitted_at' => 'datetime',
            'completed_at' => 'datetime',
        ];
    }

    /**
     * Get the client (dentist) that owns the case.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Dentist::class, 'client_id');
    }

    /**
     * Get the files for the case.
     */
    public function files(): HasMany
    {
        return $this->hasMany(CaseFile::class, 'case_id');
    }

    /**
     * Get the status badge color based on status
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match ($this->status) {
            'Received' => 'info',
            'Working' => 'warning',
            'Pending' => 'error',
            'Shipped' => 'success',
            default => 'outline',
        };
    }

    /**
     * Get the priority badge color based on priority
     */
    public function getPriorityBadgeColorAttribute(): string
    {
        return match ($this->priority) {
            'Urgent' => 'red',
            'Normal' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Scope a query to only include cases with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include cases with a specific priority.
     */
    public function scopeWithPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to only include cases for a specific client.
     */
    public function scopeForClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Mark the case as completed and set completed_at timestamp
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'Shipped',
            'completed_at' => now(),
        ]);
    }

    /**
     * Get the case duration in days
     */
    public function getDurationInDaysAttribute(): int
    {
        $endDate = $this->completed_at ?? now();
        return $this->submitted_at->diffInDays($endDate);
    }
}
