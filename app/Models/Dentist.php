<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class Dentist extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'fcm_token',
        'is_email_verified',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_email_verified' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the dentist's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn ($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    /**
     * Get the status badge color based on active status
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return $this->is_active ? 'green' : 'red';
    }

    /**
     * Get the status text
     */
    public function getStatusTextAttribute(): string
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get the email verification status badge color
     */
    public function getEmailVerificationBadgeColorAttribute(): string
    {
        return $this->is_email_verified ? 'blue' : 'yellow';
    }

    /**
     * Get the email verification status text
     */
    public function getEmailVerificationTextAttribute(): string
    {
        return $this->is_email_verified ? 'Verified' : 'Unverified';
    }

    /**
     * Get the cases for the dentist.
     */
    public function cases()
    {
        return $this->hasMany(\App\Models\DentalCase::class, 'client_id');
    }

    /**
     * Get the uploaded files for the dentist.
     */
    public function uploadedFiles()
    {
        return $this->hasMany(\App\Models\CaseFile::class, 'uploaded_by_id');
    }

    /**
     * Get the chat for the dentist.
     */
    public function chat()
    {
        return $this->hasOne(\App\Models\Chat::class);
    }
}
