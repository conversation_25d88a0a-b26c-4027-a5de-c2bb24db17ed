<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class CaseFile extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'case_id',
        'uploaded_by_id',
        'file_path',
        'file_type',
        'label',
    ];

    /**
     * Get the case that owns the file.
     */
    public function case(): BelongsTo
    {
        return $this->belongsTo(DentalCase::class, 'case_id');
    }

    /**
     * Get the dentist who uploaded the file.
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(Dentist::class, 'uploaded_by_id');
    }

    /**
     * Get the file URL
     */
    public function getFileUrlAttribute(): string
    {
        return Storage::url($this->file_path);
    }

    /**
     * Get the file size in human readable format
     */
    public function getFileSizeAttribute(): string
    {
        if (!Storage::exists($this->file_path)) {
            return 'Unknown';
        }

        $bytes = Storage::size($this->file_path);
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the file extension
     */
    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->file_path, PATHINFO_EXTENSION);
    }

    /**
     * Check if the file is an image
     */
    public function isImage(): bool
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return in_array(strtolower($this->file_extension), $imageTypes);
    }

    /**
     * Check if the file is a document
     */
    public function isDocument(): bool
    {
        $documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
        return in_array(strtolower($this->file_extension), $documentTypes);
    }

    /**
     * Get the file type icon class
     */
    public function getFileIconAttribute(): string
    {
        if ($this->isImage()) {
            return 'photo';
        }

        if ($this->isDocument()) {
            return 'document-text';
        }

        return 'document';
    }
}
