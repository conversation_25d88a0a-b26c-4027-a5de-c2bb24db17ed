<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Chat extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'dentist_id',
    ];

    /**
     * Get the dentist that owns the chat.
     */
    public function dentist(): BelongsTo
    {
        return $this->belongsTo(Dentist::class);
    }

    /**
     * Get the messages for the chat.
     */
    public function messages(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Get the latest message for the chat.
     */
    public function latestMessage(): HasMany
    {
        return $this->hasMany(Message::class)->latest();
    }

    /**
     * Get unread messages count for admin.
     */
    public function unreadMessagesCount(): int
    {
        return $this->messages()
            ->where('sender', 'dentist')
            ->where('is_unread', true)
            ->count();
    }
}
