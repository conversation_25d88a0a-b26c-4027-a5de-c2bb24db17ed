<?php

namespace App\Livewire\Admin;

use App\Models\Chat;
use App\Models\DentalCase;
use App\Models\Dentist;
use Carbon\Carbon;
use Livewire\Component;

class Dashboard extends Component
{
    public function render()
    {
        // Calculate stats
        $totalCases = DentalCase::count();
        $activeCases = DentalCase::whereIn('status', ['Received', 'Working', 'Pending'])->count();
        $completedCases = DentalCase::where('status', 'Shipped')->count();
        $todaysCases = DentalCase::whereDate('created_at', today())->count();
        $thisWeekCases = DentalCase::whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ])->count();
        $totalDentists = Dentist::count();
        $activeDentists = Dentist::where('is_active', true)->count();
        $verifiedDentists = Dentist::where('is_email_verified', true)->count();

        // Recent cases (last 5)
        $recentCases = DentalCase::with('client')
            ->latest()
            ->take(5)
            ->get();

        // Recent chats with last message (last 5)
        $recentChats = Chat::with(['dentist', 'messages' => function ($query) {
                $query->latest()->limit(1);
            }])
            ->whereHas('messages')
            ->orderByDesc(function ($query) {
                $query->select('created_at')
                    ->from('messages')
                    ->whereColumn('messages.chat_id', 'chats.id')
                    ->latest()
                    ->limit(1);
            })
            ->take(5)
            ->get();

        return view('livewire.admin.dashboard', compact(
            'totalCases',
            'activeCases',
            'completedCases',
            'todaysCases',
            'thisWeekCases',
            'totalDentists',
            'activeDentists',
            'verifiedDentists',
            'recentCases',
            'recentChats'
        ));
    }
}
