<?php

namespace App\Livewire\Admin;

use App\Models\Dentist;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('components.layouts.app')]

class DentistsManagement extends Component
{
    use WithPagination;

    public $totalDentists;
    public $activeDentists;

    // Search and filters
    public $search = '';
    public $statusFilter = '';

    // Modal states
    public $showFormModal = false;
    public $showConfirmModal = false;
    public $showDetailsModal = false;

    // Form fields
    public $dentistId = null;
    public $name = '';
    public $email = '';
    public $phone = '';
    public $password = '';
    public $password_confirmation = '';
    public $is_active = true;
    public $is_email_verified = false;

    // Modal data
    public $dentistToDelete = null;
    public $dentistDetails = null;



    public function mount(){
        $this->totalDentists = Dentist::count();
        $this->activeDentists = Dentist::where('is_active', true)->count();
    }

    public function rules()
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('dentists', 'email')->ignore($this->dentistId),
            ],
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
            'is_email_verified' => 'boolean',
        ];

        // Password is required only when creating a new dentist
        if (!$this->dentistId) {
            $rules['password'] = 'required|string|min:8|confirmed';
            $rules['password_confirmation'] = 'required';
        } else {
            $rules['password'] = 'nullable|string|min:8|confirmed';
            $rules['password_confirmation'] = 'nullable';
        }

        return $rules;
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->statusFilter = '';
        $this->resetPage();
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->showFormModal = true;
        $this->dispatch('open-form-modal');
    }

    public function openEditModal($dentistId)
    {
        $dentist = Dentist::findOrFail($dentistId);

        $this->dentistId = $dentist->id;
        $this->name = $dentist->name;
        $this->email = $dentist->email;
        $this->phone = $dentist->phone;
        $this->is_active = $dentist->is_active;
        $this->is_email_verified = $dentist->is_email_verified;
        $this->password = '';
        $this->password_confirmation = '';

        $this->showFormModal = true;
        $this->dispatch('open-form-modal');
    }

    public function openDetailsModal($dentistId)
    {
        $this->dentistDetails = Dentist::findOrFail($dentistId);
        $this->showDetailsModal = true;
        $this->dispatch('open-details-modal');
    }

    public function openDeleteModal($dentistId)
    {
        $this->dentistToDelete = Dentist::findOrFail($dentistId);
        $this->showConfirmModal = true;
        $this->dispatch('open-confirm-modal');
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'is_active' => $this->is_active,
            'is_email_verified' => $this->is_email_verified,
        ];

        if ($this->password) {
            $data['password'] = Hash::make($this->password);
        }

        if ($this->dentistId) {
            // Update existing dentist
            $dentist = Dentist::findOrFail($this->dentistId);
            $dentist->update($data);

            $this->dispatch('notify', variant: 'success', title: 'Success', message: 'Dentist updated successfully!');
        } else {
            // Create new dentist
            Dentist::create($data);

            $this->dispatch('notify', variant: 'success', title: 'Success', message: 'Dentist created successfully!');
        }

        $this->closeFormModal();
    }

    public function confirmDelete()
    {
        if ($this->dentistToDelete) {
            $this->dentistToDelete->delete();
            $this->dispatch('notify', variant: 'success', title: 'Success', message: 'Dentist deleted successfully!');
        }

        $this->closeConfirmModal();
    }

    public function toggleStatus($dentistId)
    {
        $dentist = Dentist::findOrFail($dentistId);
        $dentist->update(['is_active' => !$dentist->is_active]);

        $status = $dentist->is_active ? 'activated' : 'deactivated';
        $this->dispatch('notify', variant: 'success', title: 'Success', message: "Dentist {$status} successfully!");
    }

    public function closeFormModal()
    {
        $this->showFormModal = false;
        $this->resetForm();
        $this->dispatch('close-form-modal');
    }

    public function closeConfirmModal()
    {
        $this->showConfirmModal = false;
        $this->dentistToDelete = null;
        $this->dispatch('close-confirm-modal');
    }

    public function closeDetailsModal()
    {
        $this->showDetailsModal = false;
        $this->dentistDetails = null;
        $this->dispatch('close-details-modal');
    }

    private function resetForm()
    {
        $this->dentistId = null;
        $this->name = '';
        $this->email = '';
        $this->phone = '';
        $this->password = '';
        $this->password_confirmation = '';
        $this->is_active = true;
        $this->is_email_verified = false;
        $this->resetErrorBag();
    }

    public function render()
    {
        $dentists = Dentist::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%')
                        ->orWhere('phone', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter !== '', function ($query) {
                $query->where('is_active', $this->statusFilter);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('livewire.admin.dentists-management', [
            'dentists' => $dentists,
        ]);
    }
}
