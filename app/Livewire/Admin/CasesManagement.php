<?php

namespace App\Livewire\Admin;

use App\Models\DentalCase;
use App\Models\Dentist;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('components.layouts.app')]

class CasesManagement extends Component
{
    use WithPagination;

    public $totalCases;
    public $activeCases;
    public $completedCases;

    // Search and filters
    public $search = '';
    public $statusFilter = '';
    public $priorityFilter = '';
    public $clientFilter = '';

    // Modal states
    public $showFormModal = false;
    public $showConfirmModal = false;
    public $showDetailsModal = false;

    // Form fields
    public $caseId = null;
    public $client_id = '';
    public $patient_name = '';
    public $case_title = '';
    public $case_description = '';
    public $status = 'Received';
    public $priority = 'Normal';

    // Modal data
    public $caseToDelete = null;
    public $caseDetails = null;

    public function mount()
    {
        $this->totalCases = DentalCase::count();
        $this->activeCases = DentalCase::whereIn('status', ['Received', 'Working', 'Pending'])->count();
        $this->completedCases = DentalCase::where('status', 'Shipped')->count();
    }

    public function rules()
    {
        return [
            'client_id' => 'required|exists:dentists,id',
            'patient_name' => 'nullable|string|max:255',
            'case_title' => 'required|string|max:255',
            'case_description' => 'required|string',
            'status' => ['required', Rule::in(['Received', 'Working', 'Pending', 'Shipped'])],
            'priority' => ['required', Rule::in(['Normal', 'Urgent'])],
        ];
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedPriorityFilter()
    {
        $this->resetPage();
    }

    public function updatedClientFilter()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->statusFilter = '';
        $this->priorityFilter = '';
        $this->clientFilter = '';
        $this->resetPage();
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->showFormModal = true;
        $this->dispatch('open-form-modal');
    }

    public function openEditModal($caseId)
    {
        $case = DentalCase::findOrFail($caseId);

        $this->caseId = $case->id;
        $this->client_id = $case->client_id;
        $this->patient_name = $case->patient_name;
        $this->case_title = $case->case_title;
        $this->case_description = $case->case_description;
        $this->status = $case->status;
        $this->priority = $case->priority;

        $this->showFormModal = true;
        $this->dispatch('open-form-modal');
    }

    public function openDetailsModal($caseId)
    {
        $this->caseDetails = DentalCase::with(['client', 'files.uploadedBy'])->findOrFail($caseId);
        $this->showDetailsModal = true;
        $this->dispatch('open-details-modal');
    }

    public function openConfirmModal($caseId)
    {
        $this->caseToDelete = DentalCase::findOrFail($caseId);
        $this->showConfirmModal = true;
        $this->dispatch('open-confirm-modal');
    }

    public function save()
    {
        $this->validate();

        $data = [
            'client_id' => $this->client_id,
            'patient_name' => $this->patient_name,
            'case_title' => $this->case_title,
            'case_description' => $this->case_description,
            'status' => $this->status,
            'priority' => $this->priority,
        ];

        // Set completed_at when status is changed to Shipped
        if ($this->status === 'Shipped') {
            $data['completed_at'] = now();
        } elseif ($this->caseId) {
            // If changing from Shipped to another status, clear completed_at
            $existingCase = DentalCase::find($this->caseId);
            if ($existingCase && $existingCase->status === 'Shipped' && $this->status !== 'Shipped') {
                $data['completed_at'] = null;
            }
        }

        if ($this->caseId) {
            // Update existing case
            $case = DentalCase::findOrFail($this->caseId);
            $case->update($data);

            $this->dispatch('notify', variant: 'success', title: 'Success', message: 'Case updated successfully!');
        } else {
            // Create new case
            $data['submitted_at'] = now();
            DentalCase::create($data);

            $this->dispatch('notify', variant: 'success', title: 'Success', message: 'Case created successfully!');
        }

        $this->closeFormModal();
        $this->mount(); // Refresh stats
    }

    public function delete()
    {
        if ($this->caseToDelete) {
            $this->caseToDelete->delete();
            $this->dispatch('notify', variant: 'success', title: 'Success', message: 'Case deleted successfully!');
            $this->closeConfirmModal();
            $this->mount(); // Refresh stats
        }
    }

    public function updateStatus($caseId, $newStatus)
    {
        $case = DentalCase::findOrFail($caseId);
        $updateData = ['status' => $newStatus];

        if ($newStatus === 'Shipped') {
            $updateData['completed_at'] = now();
        } elseif ($case->status === 'Shipped' && $newStatus !== 'Shipped') {
            $updateData['completed_at'] = null;
        }

        $case->update($updateData);

        $this->dispatch('notify', variant: 'success', title: 'Success', message: 'Case status updated successfully!');
        $this->mount(); // Refresh stats
    }

    public function resetForm()
    {
        $this->caseId = null;
        $this->client_id = '';
        $this->patient_name = '';
        $this->case_title = '';
        $this->case_description = '';
        $this->status = 'Received';
        $this->priority = 'Normal';
        $this->resetValidation();
    }

    public function closeFormModal()
    {
        $this->showFormModal = false;
        $this->resetForm();
        $this->dispatch('close-form-modal');
    }

    public function closeConfirmModal()
    {
        $this->showConfirmModal = false;
        $this->caseToDelete = null;
        $this->dispatch('close-confirm-modal');
    }

    public function closeDetailsModal()
    {
        $this->showDetailsModal = false;
        $this->caseDetails = null;
        $this->dispatch('close-details-modal');
    }

    public function render()
    {
        $cases = DentalCase::query()
            ->with(['client', 'files'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('case_title', 'like', '%' . $this->search . '%')
                        ->orWhere('patient_name', 'like', '%' . $this->search . '%')
                        ->orWhere('case_description', 'like', '%' . $this->search . '%')
                        ->orWhereHas('client', function ($clientQuery) {
                            $clientQuery->where('name', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when($this->statusFilter !== '', function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->priorityFilter !== '', function ($query) {
                $query->where('priority', $this->priorityFilter);
            })
            ->when($this->clientFilter !== '', function ($query) {
                $query->where('client_id', $this->clientFilter);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $dentists = Dentist::where('is_active', true)->orderBy('name')->get();

        return view('livewire.admin.cases-management', [
            'cases' => $cases,
            'dentists' => $dentists,
        ]);
    }
}
