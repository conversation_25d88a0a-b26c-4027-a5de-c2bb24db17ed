<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('dentists')->onDelete('cascade');
            $table->string('patient_name')->nullable();
            $table->string('case_title');
            $table->text('case_description');
            $table->enum('status', ['Received', 'Working', 'Pending', 'Shipped'])->default('Received');
            $table->enum('priority', ['Normal', 'Urgent'])->default('Normal');
            $table->timestamp('submitted_at')->useCurrent();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['client_id', 'status']);
            $table->index('status');
            $table->index('priority');
            $table->index('submitted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cases');
    }
};
