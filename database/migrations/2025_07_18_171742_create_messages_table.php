<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_id')->constrained('chats')->onDelete('cascade');
            $table->enum('sender', ['admin', 'dentist']);
            $table->text('message')->nullable();
            $table->string('file')->nullable();
            $table->enum('file_type', ['image', 'video', 'document'])->nullable();
            $table->boolean('is_unread')->default(true);
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['chat_id', 'created_at']);
            $table->index(['chat_id', 'is_unread']);
            $table->index('sender');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
