<?php

namespace Database\Seeders;

use App\Models\Dentist;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DentistSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some sample dentists
        Dentist::factory(15)->create();

        // Create a few specific dentists for testing
        Dentist::factory()->verified()->active()->create([
            'name' => 'Dr. <PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+****************',
        ]);

        Dentist::factory()->unverified()->inactive()->create([
            'name' => 'Dr. <PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+****************',
        ]);

        Dentist::factory()->verified()->active()->create([
            'name' => 'Dr. <PERSON>',
            'email' => 'micha<PERSON>.<EMAIL>',
            'phone' => '+****************',
        ]);
    }
}
