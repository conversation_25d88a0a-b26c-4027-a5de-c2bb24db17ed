<?php

namespace Database\Seeders;

use App\Models\Chat;
use App\Models\Dentist;
use App\Models\Message;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ChatSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some test dentists if they don't exist
        $dentists = [];

        if (Dentist::count() < 3) {
            $dentists[] = Dentist::create([
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'password' => bcrypt('password'),
                'is_active' => true,
                'is_email_verified' => true,
            ]);

            $dentists[] = Dentist::create([
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+1234567891',
                'password' => bcrypt('password'),
                'is_active' => true,
                'is_email_verified' => true,
            ]);

            $dentists[] = Dentist::create([
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+1234567892',
                'password' => bcrypt('password'),
                'is_active' => true,
                'is_email_verified' => false,
            ]);
        } else {
            $dentists = Dentist::take(3)->get()->toArray();
        }

        // Create chats and messages for the first two dentists
        foreach (array_slice($dentists, 0, 2) as $dentist) {
            $chat = Chat::create([
                'dentist_id' => $dentist['id'] ?? $dentist->id,
            ]);

            // Create some sample messages
            Message::create([
                'chat_id' => $chat->id,
                'sender' => 'dentist',
                'message' => 'Hello, I have a question about my recent case submission.',
                'is_unread' => true,
            ]);

            Message::create([
                'chat_id' => $chat->id,
                'sender' => 'admin',
                'message' => 'Hi! I\'d be happy to help. What specific question do you have?',
                'is_unread' => false,
            ]);

            Message::create([
                'chat_id' => $chat->id,
                'sender' => 'dentist',
                'message' => 'I wanted to check on the status of case #123. When can I expect it to be completed?',
                'is_unread' => true,
            ]);

            if ($chat->dentist->name === 'Dr. John Smith') {
                Message::create([
                    'chat_id' => $chat->id,
                    'sender' => 'admin',
                    'message' => 'Let me check that for you. Case #123 is currently in the working phase and should be completed by Friday.',
                    'is_unread' => false,
                ]);

                Message::create([
                    'chat_id' => $chat->id,
                    'sender' => 'dentist',
                    'message' => 'Perfect! Thank you for the quick response.',
                    'is_unread' => true,
                ]);
            }
        }

        $this->command->info('Chat seeder completed successfully!');
    }
}
