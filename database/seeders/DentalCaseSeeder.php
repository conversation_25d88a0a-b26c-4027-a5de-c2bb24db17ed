<?php

namespace Database\Seeders;

use App\Models\DentalCase;
use App\Models\CaseFile;
use App\Models\Dentist;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DentalCaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing dentists or create some if none exist
        $dentists = Dentist::all();
        if ($dentists->isEmpty()) {
            $dentists = Dentist::factory(5)->create();
        }

        // Create cases for each dentist
        $dentists->each(function ($dentist) {
            // Create 3-8 cases per dentist
            $casesCount = fake()->numberBetween(3, 8);

            for ($i = 0; $i < $casesCount; $i++) {
                $case = DentalCase::factory()
                    ->for($dentist, 'client')
                    ->create();

                // Add 1-4 files per case
                $filesCount = fake()->numberBetween(1, 4);
                CaseFile::factory($filesCount)
                    ->for($case, 'case')
                    ->for($dentist, 'uploadedBy')
                    ->create();
            }
        });

        // Create some specific test cases with known data
        $testDentist = $dentists->first();

        // Urgent case
        $urgentCase = DentalCase::factory()
            ->urgent()
            ->received()
            ->for($testDentist, 'client')
            ->create([
                'case_title' => 'Emergency Crown Replacement - Upper Right Molar',
                'patient_name' => 'John Smith',
                'case_description' => 'Patient needs urgent crown replacement due to fracture. Please prioritize this case.',
            ]);

        CaseFile::factory(2)
            ->for($urgentCase, 'case')
            ->for($testDentist, 'uploadedBy')
            ->create();

        // Completed case
        $completedCase = DentalCase::factory()
            ->shipped()
            ->for($testDentist, 'client')
            ->create([
                'case_title' => 'Full Mouth Restoration - Veneers',
                'patient_name' => 'Sarah Johnson',
                'case_description' => 'Complete veneer set for anterior teeth. Patient consultation completed.',
            ]);

        CaseFile::factory(5)
            ->for($completedCase, 'case')
            ->for($testDentist, 'uploadedBy')
            ->create();

        // Working case
        $workingCase = DentalCase::factory()
            ->working()
            ->for($testDentist, 'client')
            ->create([
                'case_title' => 'Bridge Work - Lower Left Premolars',
                'patient_name' => 'Michael Brown',
                'case_description' => 'Three-unit bridge for missing premolar. Impressions taken and ready for fabrication.',
            ]);

        CaseFile::factory(3)
            ->for($workingCase, 'case')
            ->for($testDentist, 'uploadedBy')
            ->create();
    }
}
