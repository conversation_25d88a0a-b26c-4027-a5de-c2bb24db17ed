<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CaseFile>
 */
class CaseFileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $fileTypes = [
            ['type' => 'image/jpeg', 'ext' => 'jpg'],
            ['type' => 'image/png', 'ext' => 'png'],
            ['type' => 'application/pdf', 'ext' => 'pdf'],
            ['type' => 'image/jpeg', 'ext' => 'jpg'],
            ['type' => 'image/png', 'ext' => 'png'],
        ];

        $selectedType = fake()->randomElement($fileTypes);
        $labels = ['X-ray', 'Lab Slip', 'Impression Photo', 'Reference Image', 'Instructions', null];

        return [
            'case_id' => \App\Models\DentalCase::factory(),
            'uploaded_by_id' => \App\Models\Dentist::factory(),
            'file_path' => 'case-files/' . fake()->uuid() . '.' . $selectedType['ext'],
            'file_type' => $selectedType['type'],
            'label' => fake()->randomElement($labels),
        ];
    }

    /**
     * Indicate that the file is an image.
     */
    public function image(): static
    {
        return $this->state(fn (array $attributes) => [
            'file_type' => fake()->randomElement(['image/jpeg', 'image/png', 'image/gif']),
            'file_path' => 'case-files/' . fake()->uuid() . '.' . fake()->randomElement(['jpg', 'png', 'gif']),
        ]);
    }

    /**
     * Indicate that the file is a document.
     */
    public function document(): static
    {
        return $this->state(fn (array $attributes) => [
            'file_type' => 'application/pdf',
            'file_path' => 'case-files/' . fake()->uuid() . '.pdf',
        ]);
    }

    /**
     * Indicate that the file has a specific label.
     */
    public function withLabel(string $label): static
    {
        return $this->state(fn (array $attributes) => [
            'label' => $label,
        ]);
    }
}
