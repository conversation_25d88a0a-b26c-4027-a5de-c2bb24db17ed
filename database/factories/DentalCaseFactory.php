<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DentalCase>
 */
class DentalCaseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $submittedAt = fake()->dateTimeBetween('-3 months', 'now');
        $status = fake()->randomElement(['Received', 'Working', 'Pending', 'Shipped']);
        $completedAt = null;

        // If status is Shipped, set a completed_at date after submitted_at
        if ($status === 'Shipped') {
            $completedAt = fake()->dateTimeBetween($submittedAt, 'now');
        }

        return [
            'client_id' => \App\Models\Dentist::factory(),
            'patient_name' => fake()->optional(0.8)->name(),
            'case_title' => fake()->randomElement([
                'Crown Restoration',
                'Bridge Work',
                'Implant Crown',
                'Veneer Set',
                'Partial Denture',
                'Full Denture',
                'Orthodontic Retainer',
                'Night Guard',
                'Inlay/Onlay',
                'Temporary Crown',
            ]) . ' - ' . fake()->randomElement([
                'Upper Right',
                'Upper Left',
                'Lower Right',
                'Lower Left',
                'Full Mouth',
                'Anterior',
                'Posterior',
            ]),
            'case_description' => fake()->paragraph(3),
            'status' => $status,
            'priority' => fake()->randomElement(['Normal', 'Normal', 'Normal', 'Urgent']), // 75% Normal, 25% Urgent
            'submitted_at' => $submittedAt,
            'completed_at' => $completedAt,
        ];
    }

    /**
     * Indicate that the case is urgent.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'Urgent',
        ]);
    }

    /**
     * Indicate that the case is received.
     */
    public function received(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Received',
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the case is in working status.
     */
    public function working(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Working',
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the case is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Pending',
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the case is shipped.
     */
    public function shipped(): static
    {
        return $this->state(function (array $attributes) {
            $submittedAt = $attributes['submitted_at'] ?? fake()->dateTimeBetween('-3 months', '-1 week');
            return [
                'status' => 'Shipped',
                'completed_at' => fake()->dateTimeBetween($submittedAt, 'now'),
            ];
        });
    }
}
